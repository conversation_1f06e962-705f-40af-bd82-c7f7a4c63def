{"testName": "single-user-test-2025-07-25T09-56-10", "startTime": "2025-07-25T09:56:10.370Z", "endTime": "2025-07-25T09:57:37.802Z", "duration": 87432, "results": {"passed": 16, "failed": 0, "skipped": 1, "total": 17}, "successRate": 94.12, "errors": [], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-25T09:56:10.372Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-25T09:56:10.374Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuserryqm3b"}, "timestamp": "2025-07-25T09:56:10.377Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-25T09:56:10.379Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "f0e9ab7c-a58e-4651-aaea-d0df19df31cb", "email": "<EMAIL>"}, "timestamp": "2025-07-25T09:56:10.510Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-25T09:56:10.510Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-25T09:56:10.581Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-25T09:56:10.582Z"}, {"level": "SUCCESS", "message": "WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-25T09:56:10.606Z"}, {"level": "INFO", "message": "Starting: Step 5: Update Profile", "data": null, "timestamp": "2025-07-25T09:56:10.607Z"}, {"level": "SUCCESS", "message": "Profile updated successfully", "data": {"username": "updatedf4wog6"}, "timestamp": "2025-07-25T09:56:10.642Z"}, {"level": "INFO", "message": "Starting: Step 6: Submit Assessment", "data": null, "timestamp": "2025-07-25T09:56:10.643Z"}, {"level": "SUCCESS", "message": "Assessment submitted successfully", "data": {"jobId": "1c844ce2-390f-403c-8272-6bd5bac23f8e", "status": "queued"}, "timestamp": "2025-07-25T09:56:10.781Z"}, {"level": "INFO", "message": "Starting: Step 7: Wait for WebSocket Notification", "data": null, "timestamp": "2025-07-25T09:56:10.781Z"}, {"level": "INFO", "message": "Waiting for assessment completion notification...", "data": null, "timestamp": "2025-07-25T09:56:10.781Z"}, {"level": "SUCCESS", "message": "Assessment completion notification received", "data": {"jobId": "1c844ce2-390f-403c-8272-6bd5bac23f8e", "resultId": "026114c9-b95f-4767-a0cc-c1a81cd43960"}, "timestamp": "2025-07-25T09:56:38.700Z"}, {"level": "INFO", "message": "Starting: Step 8: Get Profile Persona", "data": null, "timestamp": "2025-07-25T09:56:38.701Z"}, {"level": "INFO", "message": "Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-25T09:56:38.701Z"}, {"level": "SUCCESS", "message": "Profile persona retrieved successfully", "data": {"archetype": "Disciplined Innovator", "careerCount": 4}, "timestamp": "2025-07-25T09:56:44.727Z"}, {"level": "INFO", "message": "Starting: Step 9: <PERSON>", "data": null, "timestamp": "2025-07-25T09:56:44.728Z"}, {"level": "SUCCESS", "message": "Chatbot conversation created", "data": {"conversationId": "8173507f-0ae9-487c-90ae-5f7a186253d2"}, "timestamp": "2025-07-25T09:56:48.874Z"}, {"level": "INFO", "message": "Sending message 1/5", "data": null, "timestamp": "2025-07-25T09:56:48.874Z"}, {"level": "SUCCESS", "message": "Message 1 sent and responded", "data": {"userMessage": "Hello, I need career guidance based on my assessme...", "assistantResponse": "Hello! I'm really glad you reached out — I'd be ho..."}, "timestamp": "2025-07-25T09:56:56.366Z"}, {"level": "INFO", "message": "Sending message 2/5", "data": null, "timestamp": "2025-07-25T09:56:57.372Z"}, {"level": "SUCCESS", "message": "Message 2 sent and responded", "data": {"userMessage": "What career paths would suit my personality type?...", "assistantResponse": "Great question — I’d love to help you discover car..."}, "timestamp": "2025-07-25T09:57:04.190Z"}, {"level": "INFO", "message": "Sending message 3/5", "data": null, "timestamp": "2025-07-25T09:57:05.194Z"}, {"level": "SUCCESS", "message": "Message 3 sent and responded", "data": {"userMessage": "Can you help me understand my strengths and weakne...", "assistantResponse": "Absolutely — I'd be honored to help you understand..."}, "timestamp": "2025-07-25T09:57:17.354Z"}, {"level": "INFO", "message": "Sending message 4/5", "data": null, "timestamp": "2025-07-25T09:57:18.357Z"}, {"level": "SUCCESS", "message": "Message 4 sent and responded", "data": {"userMessage": "What skills should I focus on developing?...", "assistantResponse": "Great question — and a very forward-thinking one! ..."}, "timestamp": "2025-07-25T09:57:30.831Z"}, {"level": "INFO", "message": "Sending message 5/5", "data": null, "timestamp": "2025-07-25T09:57:31.835Z"}, {"level": "SUCCESS", "message": "Message 5 sent and responded", "data": {"userMessage": "Thank you for the guidance!...", "assistantResponse": "You're so very welcome! 🌟  \nThank *you* for showi..."}, "timestamp": "2025-07-25T09:57:36.796Z"}, {"level": "SUCCESS", "message": "Chatbot interaction completed successfully", "data": null, "timestamp": "2025-07-25T09:57:37.799Z"}, {"level": "SKIPPED", "message": "Account cleanup", "reason": "ENABLE_CLEANUP is not true", "timestamp": "2025-07-25T09:57:37.800Z"}, {"level": "SUCCESS", "message": "Single User E2E Test completed successfully", "data": null, "timestamp": "2025-07-25T09:57:37.800Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-25T09:57:37.802Z"}]}