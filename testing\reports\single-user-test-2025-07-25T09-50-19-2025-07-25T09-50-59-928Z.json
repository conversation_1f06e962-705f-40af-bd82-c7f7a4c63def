{"testName": "single-user-test-2025-07-25T09-50-19", "startTime": "2025-07-25T09:50:19.825Z", "endTime": "2025-07-25T09:50:59.927Z", "duration": 40102, "results": {"passed": 8, "failed": 2, "skipped": 0, "total": 10}, "successRate": 80, "errors": [{"level": "ERROR", "message": "<PERSON><PERSON>bot test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:222:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:35:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-25T09:50:59.925Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:222:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:35:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-25T09:50:59.925Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-25T09:50:19.826Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-25T09:50:19.828Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuser0qvbad"}, "timestamp": "2025-07-25T09:50:19.830Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-25T09:50:19.831Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "07fa42b5-f17d-4bfb-b875-d5181a0cd881", "email": "<EMAIL>"}, "timestamp": "2025-07-25T09:50:21.002Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-25T09:50:21.002Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-25T09:50:21.443Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-25T09:50:21.443Z"}, {"level": "SUCCESS", "message": "WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-25T09:50:23.223Z"}, {"level": "INFO", "message": "Starting: Step 5: Update Profile", "data": null, "timestamp": "2025-07-25T09:50:23.223Z"}, {"level": "SUCCESS", "message": "Profile updated successfully", "data": {"username": "updatedgnf3bx"}, "timestamp": "2025-07-25T09:50:23.585Z"}, {"level": "INFO", "message": "Starting: Step 6: Submit Assessment", "data": null, "timestamp": "2025-07-25T09:50:23.586Z"}, {"level": "SUCCESS", "message": "Assessment submitted successfully", "data": {"jobId": "a76c5eaf-bdb6-41ec-86cd-82349dd11dc1", "status": "queued"}, "timestamp": "2025-07-25T09:50:24.101Z"}, {"level": "INFO", "message": "Starting: Step 7: Wait for WebSocket Notification", "data": null, "timestamp": "2025-07-25T09:50:24.102Z"}, {"level": "INFO", "message": "Waiting for assessment completion notification...", "data": null, "timestamp": "2025-07-25T09:50:24.102Z"}, {"level": "SUCCESS", "message": "Assessment completion notification received", "data": {"jobId": "a76c5eaf-bdb6-41ec-86cd-82349dd11dc1", "resultId": "a0ece7c9-84a4-42c2-83ad-ddfe09c31478"}, "timestamp": "2025-07-25T09:50:53.635Z"}, {"level": "INFO", "message": "Starting: Step 8: Get Profile Persona", "data": null, "timestamp": "2025-07-25T09:50:53.635Z"}, {"level": "INFO", "message": "Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-25T09:50:53.635Z"}, {"level": "SUCCESS", "message": "Profile persona retrieved successfully", "data": {"archetype": "Impactful Innovator", "careerCount": 4}, "timestamp": "2025-07-25T09:50:59.842Z"}, {"level": "INFO", "message": "Starting: Step 9: <PERSON>", "data": null, "timestamp": "2025-07-25T09:50:59.842Z"}, {"level": "ERROR", "message": "<PERSON><PERSON>bot test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:222:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:35:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-25T09:50:59.925Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:222:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:35:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-25T09:50:59.925Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-25T09:50:59.926Z"}]}