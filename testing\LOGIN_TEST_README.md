# Login Token Test

Script untuk menguji login dengan test user dan menampilkan jumlah token.

## Test User yang Dibuat

- **Username:** `testuser`
- **Email:** `<EMAIL>`
- **Password:** `testpassword123`
- **Token Balance:** `10,000 tokens`

## Cara Menjalankan

### 1. Pastikan Services Berjalan

```bash
# Start PostgreSQL dan services lainnya
docker-compose up postgres api-gateway auth-service -d

# Atau start semua services
docker-compose up -d
```

### 2. Jalankan Test

```bash
# Masuk ke folder testing
cd testing

# Install dependencies (jika belum)
npm install

# Jalankan test login
npm run test:login

# Atau langsung dengan node
node test-login-token.js
```

## Output yang Diharapkan

Test akan menampilkan:

1. **Health Check** - Memastikan API berjalan
2. **Login Test** - Test login dengan email
3. **Profile Test** - Mengambil data user dan token balance
4. **Token Information** - Menampilkan detail token dalam format yang rapi
5. **Test Summary** - Ringkasan hasil test

### Contoh Output

```
🚀 Starting Login Token Test Suite
API Base URL: http://localhost:3000
Test User: <EMAIL>

🧪 Running: API Health Check
✅ API Health Check - Passed (150ms)

🧪 Running: Login with Email
✅ Login with Email - Passed (250ms)

🧪 Running: Get User Profile
✅ Get User Profile - Passed (120ms)

============================================================
                    TOKEN INFORMATION
============================================================
User Details:
  ID:       5642650f-8914-47f6-befe-dd7deeab7956
  Username: testuser
  Email:    <EMAIL>

Token Balance:
  🪙 10,000 tokens

Token Status:
  ✅ Excellent - Ready for extensive testing
============================================================

============================================================
                    TEST SUMMARY
============================================================
Total Tests:    3
Passed:         3
Failed:         0
Duration:       520ms
Success Rate:   100.0%
============================================================

🔐 Logged out successfully
```

## Environment Variables

Bisa dikonfigurasi melalui file `.env` atau environment variables:

```bash
# API Base URL (default: http://localhost:3000)
API_BASE_URL=http://localhost:3000

# Request timeout dalam milliseconds (default: 30000)
TEST_TIMEOUT=30000

# Enable detailed logging (default: false)
ENABLE_DETAILED_LOGS=true
```

## Troubleshooting

### 1. Connection Error

```
❌ API Health Check - Failed
Error: connect ECONNREFUSED 127.0.0.1:3000
```

**Solusi:** Pastikan services berjalan dengan `docker-compose up -d`

### 2. Login Failed

```
❌ Login with Email - Failed
Error: Login failed: Invalid credentials
```

**Solusi:** 
- Pastikan test user sudah dibuat di database
- Cek password hash di database
- Jalankan ulang script pembuatan user

### 3. Database Connection Error

```
❌ Get User Profile - Failed
Error: Database connection failed
```

**Solusi:**
- Pastikan PostgreSQL container berjalan
- Cek environment variables database
- Restart PostgreSQL container

## Query Database Manual

Untuk mengecek test user di database:

```sql
-- Connect ke PostgreSQL
docker exec -it atma-postgres psql -U atma_user -d atma_db

-- Cek user
SELECT username, email, token_balance, is_active, created_at 
FROM auth.users 
WHERE username = 'testuser';

-- Update token balance jika perlu
UPDATE auth.users 
SET token_balance = 10000 
WHERE username = 'testuser';
```

## File yang Terkait

- `test-login-token.js` - Script utama test login
- `lib/api-client.js` - HTTP client untuk API calls
- `lib/test-logger.js` - Logger untuk menyimpan hasil test
- `reports/` - Folder untuk laporan test hasil

## Integrasi dengan Test Suite Lain

Script ini bisa diintegrasikan dengan test suite lainnya:

```bash
# Jalankan semua test termasuk login test
npm run test

# Jalankan test dengan database setup
npm run test:with-db
```
