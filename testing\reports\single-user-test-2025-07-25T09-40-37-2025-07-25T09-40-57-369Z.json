{"testName": "single-user-test-2025-07-25T09-40-37", "startTime": "2025-07-25T09:40:37.142Z", "endTime": "2025-07-25T09:40:57.367Z", "duration": 20225, "results": {"passed": 3, "failed": 2, "skipped": 0, "total": 5}, "successRate": 60, "errors": [{"level": "ERROR", "message": "WebSocket connection failed", "error": {"message": "timeout", "stack": "Error: timeout\n    at Timeout.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\socket.io-client\\build\\cjs\\manager.js:176:25)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-25T09:40:57.364Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "timeout", "stack": "Error: timeout\n    at Timeout.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\socket.io-client\\build\\cjs\\manager.js:176:25)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-25T09:40:57.365Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-25T09:40:37.143Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-25T09:40:37.145Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuseru7epp0"}, "timestamp": "2025-07-25T09:40:37.148Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-25T09:40:37.149Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "d8bb8d94-44ec-4223-a722-74503e9395e8", "email": "<EMAIL>"}, "timestamp": "2025-07-25T09:40:37.283Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-25T09:40:37.284Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-25T09:40:37.345Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-25T09:40:37.346Z"}, {"level": "ERROR", "message": "WebSocket connection failed", "error": {"message": "timeout", "stack": "Error: timeout\n    at Timeout.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\socket.io-client\\build\\cjs\\manager.js:176:25)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-25T09:40:57.364Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "timeout", "stack": "Error: timeout\n    at Timeout.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\socket.io-client\\build\\cjs\\manager.js:176:25)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-25T09:40:57.365Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-25T09:40:57.366Z"}]}