#!/usr/bin/env node

/**
 * Test Login with Test User and Display Token Count
 * 
 * This script tests the login functionality using the test user
 * created in the database and displays the current token balance.
 * 
 * Usage:
 *   node test-login-token.js
 *   npm run test:login
 * 
 * Environment Variables:
 *   API_BASE_URL - Base URL for the API (default: http://localhost:3000)
 *   TEST_TIMEOUT - Request timeout in milliseconds (default: 30000)
 *   ENABLE_DETAILED_LOGS - Enable detailed logging (default: false)
 */

require('dotenv').config();
const chalk = require('chalk');
const APIClient = require('./lib/api-client');
const TestLogger = require('./lib/test-logger');

class LoginTokenTest {
  constructor() {
    this.apiClient = new APIClient();
    this.logger = new TestLogger('login-token-test');
    this.testResults = {
      startTime: new Date(),
      endTime: null,
      success: false,
      tests: [],
      errors: [],
      userInfo: null,
      tokenInfo: null
    };
  }

  /**
   * Test user credentials (created in database)
   */
  getTestCredentials() {
    return {
      email: '<EMAIL>',
      password: 'testpassword123'
    };
  }

  /**
   * Alternative login using username
   */
  getTestCredentialsWithUsername() {
    return {
      username: 'testuser',
      password: 'testpassword123'
    };
  }

  /**
   * Run a single test and record results
   */
  async runTest(testName, testFunction) {
    const startTime = Date.now();
    console.log(chalk.blue(`\n🧪 Running: ${testName}`));
    
    try {
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      this.testResults.tests.push({
        name: testName,
        success: true,
        duration,
        result
      });
      
      console.log(chalk.green(`✅ ${testName} - Passed (${duration}ms)`));
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.testResults.tests.push({
        name: testName,
        success: false,
        duration,
        error: error.message
      });
      
      this.testResults.errors.push({
        test: testName,
        error: error.message,
        stack: error.stack
      });
      
      console.log(chalk.red(`❌ ${testName} - Failed (${duration}ms)`));
      console.log(chalk.red(`   Error: ${error.message}`));
      throw error;
    }
  }

  /**
   * Test API health check
   */
  async testHealthCheck() {
    return await this.runTest('API Health Check', async () => {
      const response = await this.apiClient.healthCheck();

      if (!response || (response.status !== 'ok' && response.status !== 'healthy')) {
        throw new Error(`API health check failed. Status: ${response?.status || 'undefined'}`);
      }

      return response;
    });
  }

  /**
   * Test login with email
   */
  async testLoginWithEmail() {
    return await this.runTest('Login with Email', async () => {
      const credentials = this.getTestCredentials();
      const response = await this.apiClient.login(credentials);
      
      if (!response.success) {
        throw new Error(`Login failed: ${response.message || 'Unknown error'}`);
      }
      
      if (!response.data.token) {
        throw new Error('No token received in login response');
      }
      
      if (!response.data.user) {
        throw new Error('No user data received in login response');
      }
      
      // Store user info for later use
      this.testResults.userInfo = response.data.user;
      
      return response;
    });
  }

  /**
   * Test login with username (alternative)
   */
  async testLoginWithUsername() {
    return await this.runTest('Login with Username (Alternative)', async () => {
      // Clear previous auth first
      this.apiClient.clearAuth();
      
      const credentials = this.getTestCredentialsWithUsername();
      const response = await this.apiClient.login(credentials);
      
      if (!response.success) {
        throw new Error(`Login failed: ${response.message || 'Unknown error'}`);
      }
      
      return response;
    });
  }

  /**
   * Test getting user profile and token balance
   */
  async testGetProfile() {
    return await this.runTest('Get User Profile', async () => {
      const response = await this.apiClient.getProfile();
      
      if (!response.success) {
        throw new Error(`Get profile failed: ${response.message || 'Unknown error'}`);
      }
      
      if (!response.data.user) {
        throw new Error('No user data in profile response');
      }
      
      // Store token info
      this.testResults.tokenInfo = {
        balance: response.data.user.token_balance,
        userId: response.data.user.id,
        username: response.data.user.username,
        email: response.data.user.email
      };
      
      return response;
    });
  }

  /**
   * Display token information in a formatted way
   */
  displayTokenInfo() {
    console.log(chalk.cyan('\n' + '='.repeat(60)));
    console.log(chalk.cyan('                    TOKEN INFORMATION'));
    console.log(chalk.cyan('='.repeat(60)));
    
    if (this.testResults.tokenInfo) {
      const { balance, userId, username, email } = this.testResults.tokenInfo;
      
      console.log(chalk.white('User Details:'));
      console.log(chalk.gray(`  ID:       ${userId}`));
      console.log(chalk.gray(`  Username: ${username}`));
      console.log(chalk.gray(`  Email:    ${email}`));
      console.log();
      
      console.log(chalk.white('Token Balance:'));
      if (balance >= 10000) {
        console.log(chalk.green(`  🪙 ${balance.toLocaleString()} tokens`));
      } else if (balance >= 1000) {
        console.log(chalk.yellow(`  🪙 ${balance.toLocaleString()} tokens`));
      } else {
        console.log(chalk.red(`  🪙 ${balance.toLocaleString()} tokens`));
      }
      
      console.log();
      console.log(chalk.white('Token Status:'));
      if (balance >= 10000) {
        console.log(chalk.green('  ✅ Excellent - Ready for extensive testing'));
      } else if (balance >= 5000) {
        console.log(chalk.yellow('  ⚠️  Good - Sufficient for moderate testing'));
      } else if (balance >= 1000) {
        console.log(chalk.yellow('  ⚠️  Low - Limited testing capacity'));
      } else {
        console.log(chalk.red('  ❌ Critical - Insufficient tokens for testing'));
      }
    } else {
      console.log(chalk.red('  ❌ No token information available'));
    }
    
    console.log(chalk.cyan('='.repeat(60)));
  }

  /**
   * Display test summary
   */
  displaySummary() {
    const totalTests = this.testResults.tests.length;
    const passedTests = this.testResults.tests.filter(t => t.success).length;
    const failedTests = totalTests - passedTests;
    const duration = this.testResults.endTime - this.testResults.startTime;
    
    console.log(chalk.cyan('\n' + '='.repeat(60)));
    console.log(chalk.cyan('                    TEST SUMMARY'));
    console.log(chalk.cyan('='.repeat(60)));
    
    console.log(chalk.white(`Total Tests:    ${totalTests}`));
    console.log(chalk.green(`Passed:         ${passedTests}`));
    console.log(chalk.red(`Failed:         ${failedTests}`));
    console.log(chalk.gray(`Duration:       ${duration}ms`));
    console.log(chalk.gray(`Success Rate:   ${((passedTests / totalTests) * 100).toFixed(1)}%`));
    
    if (this.testResults.errors.length > 0) {
      console.log(chalk.red('\nErrors:'));
      this.testResults.errors.forEach((error, index) => {
        console.log(chalk.red(`  ${index + 1}. ${error.test}: ${error.error}`));
      });
    }
    
    console.log(chalk.cyan('='.repeat(60)));
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log(chalk.blue('🚀 Starting Login Token Test Suite'));
    console.log(chalk.gray(`API Base URL: ${this.apiClient.baseURL}`));
    console.log(chalk.gray(`Test User: <EMAIL>`));
    
    try {
      // Test 1: Health Check
      await this.testHealthCheck();
      
      // Test 2: Login with Email
      await this.testLoginWithEmail();
      
      // Test 3: Get Profile and Token Balance
      await this.testGetProfile();
      
      // Test 4: Try alternative login method (optional)
      try {
        await this.testLoginWithUsername();
      } catch (error) {
        console.log(chalk.yellow('   Note: Username login not supported, email login working fine'));
      }
      
      this.testResults.success = true;
      
    } catch (error) {
      console.log(chalk.red(`\n💥 Test suite failed: ${error.message}`));
      this.testResults.success = false;
    } finally {
      this.testResults.endTime = new Date();
      
      // Display results
      this.displayTokenInfo();
      this.displaySummary();
      
      // Save test report
      await this.logger.saveReport(this.testResults);
      
      // Cleanup
      if (this.apiClient.isAuthenticated()) {
        try {
          await this.apiClient.logout();
          console.log(chalk.gray('\n🔐 Logged out successfully'));
        } catch (error) {
          console.log(chalk.yellow(`⚠️  Logout warning: ${error.message}`));
        }
      }
    }
    
    return this.testResults.success;
  }
}

// Main execution
async function main() {
  const test = new LoginTokenTest();
  
  try {
    const success = await test.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error(chalk.red(`Fatal error: ${error.message}`));
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = LoginTokenTest;
